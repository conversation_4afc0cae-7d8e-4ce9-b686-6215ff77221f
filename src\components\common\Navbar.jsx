import gsap from "gsap";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { BsPlusLg } from "react-icons/bs";
import { RxHamburgerMenu } from "react-icons/rx";
import MobileMenu from "./MobileMenu";
import { usePathname } from "next/navigation";
import { FiSearch } from "react-icons/fi";
import SearchNavbar from "./SearchNavbar";
import Button from "./Button";
import Image from "next/image";
import BackToTop from "@/components/common/BacktoTop";

const Navbar = ({ menu }) => {
	const router = useRouter();
	const pathname = usePathname();
	const [showMenu, setShowMenu] = useState(false);
	const [openSearch, setOpenSearch] = useState(false);

	useEffect(() => {
		const handleMediaQuery = window.matchMedia("(min-width: 1024px)");

		const pluscross = document.querySelector(".Plus_btn");
		const header_right_part2Nav_ul = document.querySelector(".header_right_part2 nav ul");
		const header_right_part2Nav = document.querySelector(".header_right_part2 nav");
		const Navbar2GridCntr = document.querySelector(".Navbar_wrapper2");
		const pluscrossSvg = document.querySelector(".Plus_btn svg");
		const navbarlogomenu = document.querySelector(".Navbar_logo-menu");
		const navLinks = document.querySelectorAll(".header_right_part2 nav ul li a");

		// const tl = gsap.timeline();
		const tl = gsap.timeline({ paused: true, reversed: true });
		tl.to(navLinks, { opacity: 0, duration: 0.8, ease: "linear" })
			.to(
				".header_right_part2 nav ul li:last-child",
				{
					position: "absolute",
					top: "0",
					right: "0",
					duration: 1,
					top: "100%", // Instant change
				},
				"a"
			)
			.to(
				".DropdownMenu-wrapper",
				{
					top: 0,
					duration: 0.5,
				},
				"a"
			)
			.to(
				header_right_part2Nav_ul,
				{
					textAlign: "right",
					flexDirection: "column",
					duration: 1,
					ease: "linear",
					pointerEvents: "none",
				},
				"a"
			) // overlaps with previous animation
			.to(
				".Navbar_logo2 img",
				{
					height: "18vw",
					duration: 1,
					ease: "linear",
				},
				"a"
			)

			.to(pluscrossSvg, { rotation: 135, duration: 0.3, ease: "linear", scale: 2 }, "a")
			.to(
				".shopImgnav2",
				{
					opacity: 1,
					duration: 0.3,
					ease: "linear",
					pointerEvents: "auto",
					zIndex: "99",
				},
				"a +=0.3"
			)
			.to(
				".Navbar_logo2 h3",
				{
					opacity: 1,
					duration: 0.3,
					ease: "linear",
				},
				"a +=0.3"
			);

		const handlePlusClick = (e) => {
			e.preventDefault();

			// Toggle timeline
			if (tl.reversed()) {
				tl.play();
				document.body.style.overflow = "hidden"; // prevent scroll
			} else {
				tl.reverse();
				document.body.style.overflow = "auto"; // restore scroll
			}
		};

		const resetOnRouteChange = () => {
			// flag = 0;
			document.body.style.overflow = "auto";
			document.documentElement.style.overflow = "";
			navLinks.forEach((el) => {
				el.style.opacity = 1;
				el.style.pointerEvents = "auto";
			});
		};

		if (handleMediaQuery.matches) {
			pluscross.addEventListener("click", handlePlusClick);
			router.events.on("routeChangeStart", resetOnRouteChange);
		}

		return () => {
			if (handleMediaQuery.matches) {
				pluscross.removeEventListener("click", handlePlusClick);
				router.events.off("routeChangeStart", resetOnRouteChange);
			}
			tl.kill();
		};
	}, [router.events]);
	const toggleMenu = () => {
		setShowMenu(true);
	};
	// Close the mobile menu
	const closeMenu = () => {
		setShowMenu(false);
	};

	return (
		<>
			<div className="DropdownMenu-wrapper">
				<div className="DropdownMenu-cntr">
					<div className="container">
						<div className="DropdownMenu-cntr-content">
							<div className="dropdown-wrapper container">
								<div className="Navbar_wrapper logo-wrapper">
									<div className="header_right_part"></div>
								</div>

								<div className="dropdown-top-links">
									{menu.map((item, index) => {
										return (
											<div className="single-dropdown-col" key={`menu-dropdown-items-${index}`}>
												<p>
													<Link href={item.link}>{item.name}</Link>
												</p>
												{item.submenus.length > 0 && (
													<div className="dropdown-links">
														{item.submenus.map((submenu, subIndex) => {
															return (
																<div key={`submenu-dropdown-items-${subIndex}`}>
																	<Link className="animated-link-underline" href={submenu.link}>
																		{submenu.name}
																	</Link>
																</div>
															);
														})}
													</div>
												)}
											</div>
										);
									})}{" "}
									{/* <div className="single-dropdown-col">
                    <p>
                      <Link
                        href="https://shop.yudirect.biz/ManifestLife/Subscribe.php"
                        target="blank"
                      >
                        Subscribe
                      </Link>
                    </p>
                  </div> */}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className="header_wrapper2">
				<div className="container">
					<header className="Navbar_wrapper2">
						<div className="Navbar_logo2">
							<Link href="/">
								<h3>[Curate Beautiful. Create Happy] </h3>
								<img src="../../../logo svg.svg" alt="" />
							</Link>
						</div>
						<div className="header_right_part2">
							<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
								<p className="subscribe_text">
									<Link href="https://shop.yudirect.biz/ManifestLife/Subscribe.php" target="blank">
										Subscribe
									</Link>
								</p>
								<BsPlusLg className="nav_hamburger2" onClick={toggleMenu} />
							</div>
							<div className="shopImgnav2">
								<div className="testimonial_product_navImg">
									<div className="tp_wrapper_navImg">
										<div className="tp_media_navImg">
											<div className="tp_image_navImg">
												<Image
													id="block-testimonial_XHMbWy-34619739308283"
													className=""
													sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px"
													loading="lazy"
													fill
													objectFit="contain"
													alt="Khushi Kapoor on Manifest Cover"
													src="/Assets/Manifest-Artboard-2.jpg"
												/>
											</div>
											<div className="tp_button tp_button_Imgnav">
												<Button
													href={"https://shop.yudirect.biz/ManifestLife/Subscribe.php"}
													target={"_blank"}
												>
													Subscribe
												</Button>
												{/* <Link herf=""></Link> */}
											</div>
										</div>
									</div>
								</div>
							</div>
							<nav>
								<ul>
									{menu.map((item, index) => {
										return (
											<li key={`nav-menu-${index}`}>
												<Link
													className={pathname.startsWith(item.link) ? "nav_active" : ""}
													href={item.link}
												>
													{item.name}
												</Link>
											</li>
										);
									})}
									<li>
										<div className="Plus_btn">
											<BsPlusLg />
										</div>
									</li>
								</ul>
							</nav>
						</div>
					</header>
					<header className="Navbar_wrapperMbl">
						<div className="Navbar_logo2mbl">
							<Link href="/">
								<img src="../../../logo svg.svg" alt="" />
							</Link>
						</div>
						<div className="header_right_part2">
							<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
								<p className="subscribe_text">
									<Link href="https://shop.yudirect.biz/ManifestLife/Subscribe.php" target="blank">
										Subscribe
									</Link>
								</p>
								<BsPlusLg className="nav_hamburger2" onClick={toggleMenu} />
							</div>
						</div>
					</header>
				</div>
			</div>
			<MobileMenu menu={menu} closeMenu={closeMenu} showMenu={showMenu} setShowMenu={setShowMenu} />
			<SearchNavbar openSearch={openSearch} setOpenSearch={setOpenSearch} />
			{!showMenu && (
				<div
					className="card-actions_actions_button searchbtn"
					onClick={() => {
						setOpenSearch(!openSearch);
					}}
				>
					<FiSearch />
				</div>
			)}
			{!showMenu && <BackToTop />}
		</>
	);
};

export default Navbar;
