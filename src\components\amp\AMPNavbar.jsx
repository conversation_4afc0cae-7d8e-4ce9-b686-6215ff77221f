import React from "react";

// CSS-in-JS styles for AMP navbar
const ampNavbarCSS = `
  .amp-header-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
    background-color: #faf7f3;
    font-family: var(--primary-font-scotch-display, Arial, sans-serif);
    transform: translateZ(0);
  }

  .amp-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    padding-bottom: 1.4rem;
  }

  .amp-navbar-wrapper {
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    padding: 1rem 0;
    height: 3rem;
    align-items: center;
  }

  .amp-navbar-logo {
    position: relative;
    z-index: 8;
    text-decoration: none;
    color: inherit;
  }

  .amp-navbar-logo h3 {
    font-family: var(--primary-font-Neue, Arial, sans-serif);
    font-weight: 400;
    margin: 0;
    margin-top: 1rem;
    color: #000;
    font-size: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .amp-header-right {
    position: relative;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .amp-subscribe-text {
    display: block;
    font-size: 1.1rem;
    font-family: var(--primary-font-scotch-display-compress, Arial, sans-serif);
    margin-right: 8px;
  }

  .amp-subscribe-text a {
    color: #000;
    text-decoration: none;
  }

  .amp-hamburger {
    display: block;
    font-size: 2rem;
    cursor: pointer;
    background: none;
    border: none;
    color: #000;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .amp-nav {
    position: relative;
    text-align: right;
    display: none;
    flex-direction: column;
    height: 90%;
    margin-top: 0.5rem;
  }

  .amp-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: none;
  }

  .amp-nav ul li {
    position: absolute;
    right: 0;
    pointer-events: all;
    font-weight: 500;
    padding: 0.2rem 0;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
  }

  .amp-nav ul li a {
    color: gray;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
  }

  .amp-nav ul li a:hover,
  .amp-nav ul li a.amp-nav-active {
    color: rgba(0, 0, 0, 1);
  }

  .amp-plus-btn {
    text-align: right;
    cursor: pointer;
    background: none;
    border: none;
    font-size: 2rem;
    color: #000;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
  }

  .amp-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #faf7f3;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    z-index: 998;
  }

  .amp-dropdown-content {
    padding: 2rem 0;
  }

  .amp-dropdown-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
  }

  .amp-single-dropdown-col p {
    margin: 0.5rem 0;
  }

  .amp-single-dropdown-col a {
    color: #000;
    text-decoration: none;
    font-size: 1.1rem;
    transition: color 0.3s ease;
  }

  .amp-single-dropdown-col a:hover {
    color: #666;
  }

  /* Mobile styles */
  .amp-navbar-wrapper-mobile {
    display: block;
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    padding: 1rem 0;
    height: 3rem;
    align-items: center;
  }

  .amp-navbar-logo-mobile {
    position: relative;
    z-index: 8;
    text-decoration: none;
    color: inherit;
  }

  /* Desktop styles */
  @media only screen and (min-width: 1024px) {
    .amp-navbar-wrapper {
      height: 4rem;
      grid-template-columns: 1fr 2.5fr;
      padding: 1.3rem 0;
    }

    .amp-navbar-logo h3 {
      display: initial;
      opacity: 1;
      font-size: 1.2rem;
    }

    .amp-nav {
      display: flex;
    }

    .amp-nav ul {
      display: initial;
    }

    .amp-nav ul li {
      opacity: 1;
      transform: translateY(0);
      font-size: 1.1rem;
    }

    .amp-hamburger {
      display: none;
    }

    .amp-subscribe-text {
      display: none;
    }

    .amp-navbar-wrapper-mobile {
      display: none;
    }
  }

  @media only screen and (min-width: 1300px) {
    .amp-nav ul li {
      font-size: 1.4rem;
    }

    .amp-navbar-logo h3 {
      font-size: 1.5rem;
    }

    .amp-navbar-wrapper {
      grid-template-columns: 1fr 2fr;
    }
  }

  @media only screen and (min-width: 1600px) {
    .amp-navbar-wrapper {
      grid-template-columns: 1fr 1.5fr;
    }
  }

  @media only screen and (max-width: 1023px) {
    .amp-navbar-wrapper {
      display: none;
    }
  }

  /* AMP-specific animations and states */
  .amp-menu-open .amp-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .amp-menu-open .amp-plus-btn {
    transform: rotate(135deg) scale(2);
  }

  .amp-menu-open .amp-navbar-logo h3 {
    opacity: 1;
  }

  .amp-menu-open .amp-nav ul li {
    opacity: 0;
    pointer-events: none;
  }
`;

const AMPNavbar = ({ menu = [] }) => {
	return (
		<>
			<style dangerouslySetInnerHTML={{ __html: ampNavbarCSS }} />

			{/* Dropdown Menu */}
			<div className="amp-dropdown-menu" id="amp-dropdown">
				<div className="amp-dropdown-content">
					<div className="amp-container">
						<div className="amp-dropdown-wrapper">
							{menu.map((item, index) => (
								<div key={index} className="amp-single-dropdown-col">
									<p>
										<a href={item.href || "#"}>{item.name}</a>
									</p>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>

			{/* Main Header */}
			<div className="amp-header-wrapper">
				<div className="amp-container">
					{/* Desktop Header */}
					<header className="amp-navbar-wrapper">
						<div className="amp-navbar-logo">
							<a href="/">
								<h3>[Curate Beautiful. Create Happy]</h3>
							</a>
						</div>
						<div className="amp-header-right">
							<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
								<p className="amp-subscribe-text">
									<a
										href="https://shop.yudirect.biz/ManifestLife/Subscribe.php"
										target="_blank"
										rel="noopener"
									>
										Subscribe
									</a>
								</p>
								<button
									className="amp-hamburger"
									on="tap:amp-dropdown.toggleClass(class='amp-menu-open'),amp-header.toggleClass(class='amp-menu-open')"
									aria-label="Toggle menu"
								>
									&#43;
								</button>
							</div>
							<nav className="amp-nav">
								<ul>
									{menu.map((item, index) => (
										<li key={index} style={{ top: `${index * 2.5}rem` }}>
											<a href={item.href || "#"}>{item.name}</a>
										</li>
									))}
									<li style={{ top: `${menu.length * 2.5}rem` }}>
										<button
											className="amp-plus-btn"
											on="tap:amp-dropdown.toggleClass(class='amp-menu-open'),amp-header.toggleClass(class='amp-menu-open')"
											aria-label="Toggle menu"
										>
											&#43;
										</button>
									</li>
								</ul>
							</nav>
						</div>
					</header>

					{/* Mobile Header */}
					<header className="amp-navbar-wrapper-mobile">
						<div className="amp-navbar-logo-mobile">
							<a href="/">
								<h3>[Curate Beautiful. Create Happy]</h3>
							</a>
						</div>
						<div className="amp-header-right">
							<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
								<p className="amp-subscribe-text">
									<a
										href="https://shop.yudirect.biz/ManifestLife/Subscribe.php"
										target="_blank"
										rel="noopener"
									>
										Subscribe
									</a>
								</p>
								<button
									className="amp-hamburger"
									on="tap:amp-dropdown.toggleClass(class='amp-menu-open'),amp-header.toggleClass(class='amp-menu-open')"
									aria-label="Toggle menu"
								>
									&#43;
								</button>
							</div>
						</div>
					</header>
				</div>
			</div>
		</>
	);
};

export default AMPNavbar;
