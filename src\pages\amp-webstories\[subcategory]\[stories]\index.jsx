import React from "react";
import Head from "next/head";
import { getWebStories } from "@/pages/api/WebStoriesApi";
import { convertToISTISOString, dateFormateWithTimeShort, htmlParser } from "@/utils/Util";
import { Const } from "@/utils/Constants";
import { usePathname } from "next/navigation";

const ampStoryCSS = `
  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  }
  
  .amp-story-container {
    width: 100%;
    height: 100vh;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .amp-story-player-wrapper {
    width: 100%;
    max-width: 400px;
    height: 100vh;
    max-height: 700px;
    position: relative;
  }
  
  .amp-story-close {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: rgba(0,0,0,0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
  }
  
  .amp-story-close:hover {
    background: rgba(0,0,0,0.8);
  }
  
  @media (max-width: 768px) {
    .amp-story-player-wrapper {
      width: 100%;
      height: 100vh;
      max-width: none;
      max-height: none;
    }
  }
`;

const AMPSeoHeader = ({ meta, data }) => {
	return (
		<Head>
			<title>{meta?.title || data?.slides?.[0]?.title || "AMP Web Story"}</title>
			<meta
				name="description"
				content={meta?.description || data?.slides?.[0]?.description || ""}
			/>
			<meta name="keywords" content={meta?.keywords || ""} />
			<link rel="canonical" href={meta?.canonical || ""} />
			<script async src="https://cdn.ampproject.org/v0.js"></script>
			<script
				async
				custom-element="amp-story"
				src="https://cdn.ampproject.org/v0/amp-story-1.0.js"
			></script>
			<script
				async
				custom-element="amp-img"
				src="https://cdn.ampproject.org/v0/amp-img-0.1.js"
			></script>
			<script
				async
				custom-element="amp-video"
				src="https://cdn.ampproject.org/v0/amp-video-0.1.js"
			></script>
			<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1" />
			<style amp-boilerplate="">{`body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`}</style>
			<noscript>
				<style amp-boilerplate="">{`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}</style>
			</noscript>
			<style amp-custom="">{ampStoryCSS}</style>
		</Head>
	);
};

const AMPBreadcrumbSchema = ({ breadcrumbs }) => {
	const breadcrumbSchema = {
		"@context": "https://schema.org",
		"@type": "BreadcrumbList",
		itemListElement: breadcrumbs.map((breadcrumb, index) => ({
			"@type": "ListItem",
			position: index + 1,
			name: breadcrumb.name,
			item: breadcrumb.url,
		})),
	};

	return (
		<script
			type="application/ld+json"
			dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
		/>
	);
};

const AMPImageGallerySchema = ({ title, description, url, datePublished, data }) => {
	const imageGallerySchema = {
		"@context": "https://schema.org",
		"@type": "ImageGallery",
		name: title,
		description: description,
		url: url,
		datePublished: datePublished,
		image: data.map((slide) => ({
			"@type": "ImageObject",
			url: slide.image,
			name: slide.title,
			description: slide.description,
		})),
	};

	return (
		<script
			type="application/ld+json"
			dangerouslySetInnerHTML={{ __html: JSON.stringify(imageGallerySchema) }}
		/>
	);
};

const AMPWebStoryDetail = ({ meta, data, breadcrumbs }) => {
	const pathname = usePathname();

	if (!data || !data.slides || data.slides.length === 0) {
		return (
			<>
				<AMPSeoHeader meta={meta} data={data} />
				<div className="amp-story-container">
					<p>Story not found</p>
				</div>
			</>
		);
	}

	return (
		<>
			<AMPSeoHeader meta={meta} data={data} />
			<AMPBreadcrumbSchema breadcrumbs={breadcrumbs} />
			<AMPImageGallerySchema
				title={data?.slides?.[0]?.title || ""}
				description={data?.slides?.[0]?.description || ""}
				url={Const.ClientLink + pathname}
				datePublished={data?.timestamp || ""}
				data={data?.slides || []}
			/>

			<div className="amp-story-container">
				<button className="amp-story-close" onClick={() => window.history.back()}>
					×
				</button>

				<div className="amp-story-player-wrapper">
					<amp-story
						standalone
						title={data?.slides?.[0]?.title || ""}
						publisher="Manifest Magazine"
						publisher-logo-src="/favicon.ico"
						poster-portrait-src={data?.slides?.[0]?.image || ""}
						poster-square-src={data?.slides?.[0]?.image || ""}
						poster-landscape-src={data?.slides?.[0]?.image || ""}
					>
						{data.slides.map((slide, index) => (
							<amp-story-page key={index} id={`page-${index + 1}`}>
								<amp-story-grid-layer template="fill">
									<amp-img
										src={slide.image}
										width="720"
										height="1280"
										layout="responsive"
										alt={slide.altName || slide.title}
									/>
								</amp-story-grid-layer>

								{(slide.title || slide.description) && (
									<amp-story-grid-layer template="vertical">
										{slide.title && (
											<h1
												style={{
													color: "white",
													textAlign: "center",
													textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
													fontSize: "2rem",
													fontWeight: "bold",
													margin: "20px",
												}}
											>
												{slide.title}
											</h1>
										)}

										{slide.description && (
											<div
												style={{
													color: "white",
													textAlign: "center",
													textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
													fontSize: "1.2rem",
													margin: "20px",
													lineHeight: "1.4",
												}}
											>
												{htmlParser(slide.description)}
											</div>
										)}

										{index === 0 && slide.timestamp && (
											<p
												style={{
													color: "white",
													textAlign: "center",
													textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
													fontSize: "0.9rem",
													margin: "10px",
													opacity: "0.8",
												}}
											>
												{dateFormateWithTimeShort(slide.timestamp)}
											</p>
										)}

										{slide.contributor && slide.contributor.length > 0 && (
											<p
												style={{
													color: "white",
													textAlign: "center",
													textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
													fontSize: "0.8rem",
													margin: "10px",
													opacity: "0.7",
												}}
											>
												Photo Credit: {slide.contributor.join(", ")}
											</p>
										)}
									</amp-story-grid-layer>
								)}
							</amp-story-page>
						))}

						<amp-story-bookend src="/api/bookend" layout="nodisplay" />
					</amp-story>
				</div>
			</div>
		</>
	);
};

export default AMPWebStoryDetail;

export async function getServerSideProps({ params }) {
	const { subcategory, stories } = params;
	const slug = `/webstories/${subcategory}/${stories}`;

	try {
		const response = await getWebStories(slug);

		if (!response?.data?.isExists) {
			return { notFound: true };
		}

		const data = response?.data?.data || {};
		const breadcrumbs = response?.data?.breadcrumbs || [];
		const meta = response?.data?.meta || {};

		return {
			props: {
				data,
				breadcrumbs,
				meta,
				subcategory,
				stories,
			},
		};
	} catch (error) {
		console.error("Error fetching story data:", error.message);
		return { notFound: true };
	}
}
